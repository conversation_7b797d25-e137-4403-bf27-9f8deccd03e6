import { getDbConnection } from '../utils/db'

export default defineEventHandler(async (event) => {
  try {
    const subscription = await readBody(event)
    
    if (!subscription || !subscription.endpoint) {
      throw new Error('Invalid subscription data')
    }

    const db = await getDbConnection()
    
    // Store subscription in database
    const insertQuery = `
      INSERT INTO push_subscriptions (endpoint, p256dh, auth, created_at)
      VALUES (?, ?, ?, NOW())
      ON DUPLICATE KEY UPDATE
      p256dh = VALUES(p256dh),
      auth = VALUES(auth),
      updated_at = NOW()
    `
    
    await db.execute(insertQuery, [
      subscription.endpoint,
      subscription.keys?.p256dh || '',
      subscription.keys?.auth || ''
    ])

    return { 
      status: 'success', 
      message: 'Subscription saved successfully' 
    }
    
  } catch (error) {
    console.error('Push subscription error:', error)
    return {
      status: 'error',
      message: error.message || 'Failed to save subscription'
    }
  }
})
