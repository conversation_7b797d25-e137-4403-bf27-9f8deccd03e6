import webpush from 'web-push'
import { getDbConnection } from '../utils/db'

// Configure web-push with VAPID keys
// You'll need to generate these keys using: npx web-push generate-vapid-keys
const vapidKeys = {
  publicKey: process.env.VAPID_PUBLIC_KEY || 'BLGUJ2WpT5_WjlqPcFSei5EWqdv4FcnPkNS_CIWuUzazpsanAeoJj3L5yywG-XMlB35xvgXWoYfthpL54Rm_v0Q',
  privateKey: process.env.VAPID_PRIVATE_KEY || 'rHYGoEHDULYTWDl1z1ZD-HYsL8fKAeRk6m1g7kVjfb0'
}

// Only set VAPID details if we have valid keys
if (vapidKeys.publicKey !== 'YOUR_VAPID_PUBLIC_KEY' && vapidKeys.privateKey !== 'YOUR_VAPID_PRIVATE_KEY') {
  try {
    webpush.setVapidDetails(
      'mailto:<EMAIL>', // Replace with your email
      vapidKeys.publicKey,
      vapidKeys.privateKey
    )
  } catch (error) {
    console.warn('VAPID configuration error:', error.message)
  }
}

export default defineEventHandler(async (event) => {
  try {
    const { title, body, icon, data } = await readBody(event)
    
    if (!title || !body) {
      throw new Error('Title and body are required')
    }

    const db = await getDbConnection()
    
    // Get all active subscriptions
    const [subscriptions] = await db.execute(
      'SELECT endpoint, p256dh, auth FROM push_subscriptions'
    )

    if (subscriptions.length === 0) {
      return {
        status: 'success',
        message: 'No active subscriptions found'
      }
    }

    const payload = JSON.stringify({
      title,
      body,
      icon: icon || '/monitor.png',
      badge: '/monitor.png',
      data: data || {},
      actions: [
        {
          action: 'open',
          title: 'Open Dashboard'
        }
      ]
    })

    // Send notifications to all subscriptions
    const promises = subscriptions.map(async (sub) => {
      try {
        const subscription = {
          endpoint: sub.endpoint,
          keys: {
            p256dh: sub.p256dh,
            auth: sub.auth
          }
        }

        await webpush.sendNotification(subscription, payload)
        return { success: true, endpoint: sub.endpoint }
      } catch (error) {
        console.error('Failed to send to:', sub.endpoint, error)
        
        // Remove invalid subscriptions
        if (error.statusCode === 410 || error.statusCode === 404) {
          await db.execute('DELETE FROM push_subscriptions WHERE endpoint = ?', [sub.endpoint])
        }
        
        return { success: false, endpoint: sub.endpoint, error: error.message }
      }
    })

    const results = await Promise.all(promises)
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length

    return {
      status: 'success',
      message: `Notifications sent: ${successful} successful, ${failed} failed`,
      results
    }
    
  } catch (error) {
    console.error('Send notification error:', error)
    return {
      status: 'error',
      message: error.message || 'Failed to send notifications'
    }
  }
})
