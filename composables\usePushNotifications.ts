import { ref } from 'vue'

export const usePushNotifications = () => {
  const isSupported = ref(false)
  const isSubscribed = ref(false)
  const subscription = ref<PushSubscription | null>(null)

  // VAPID public key - generated using npx web-push generate-vapid-keys
  const vapidPublicKey = 'BLGUJ2WpT5_WjlqPcFSei5EWqdv4FcnPkNS_CIWuUzazpsanAeoJj3L5yywG-XMlB35xvgXWoYfthpL54Rm_v0Q'

  const checkSupport = () => {
    isSupported.value = 'serviceWorker' in navigator && 'PushManager' in window
    return isSupported.value
  }

  const requestPermission = async () => {
    if (!checkSupport()) {
      throw new Error('Push notifications are not supported')
    }

    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }

  const subscribe = async () => {
    if (!checkSupport()) {
      throw new Error('Push notifications are not supported')
    }

    try {
      const registration = await navigator.serviceWorker.ready
      
      const sub = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      })

      subscription.value = sub
      isSubscribed.value = true

      // Send subscription to your server
      await sendSubscriptionToServer(sub)
      
      return sub
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error)
      throw error
    }
  }

  const unsubscribe = async () => {
    if (!subscription.value) return

    try {
      await subscription.value.unsubscribe()
      subscription.value = null
      isSubscribed.value = false
      
      // Remove subscription from your server
      await removeSubscriptionFromServer()
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error)
      throw error
    }
  }

  const sendSubscriptionToServer = async (sub: PushSubscription) => {
    // Send subscription to your backend
    try {
      await fetch('/api/push-subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sub)
      })
    } catch (error) {
      console.error('Failed to send subscription to server:', error)
    }
  }

  const removeSubscriptionFromServer = async () => {
    // Remove subscription from your backend
    try {
      await fetch('/api/push-unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
    } catch (error) {
      console.error('Failed to remove subscription from server:', error)
    }
  }

  const sendTestNotification = async () => {
    if (!isSubscribed.value) {
      throw new Error('Not subscribed to push notifications')
    }

    try {
      await fetch('/api/send-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Mesmer Dashboard',
          body: 'Test notification from your PWA!',
          icon: '/monitor.png'
        })
      })
    } catch (error) {
      console.error('Failed to send test notification:', error)
      throw error
    }
  }

  // Helper function to convert VAPID key
  const urlBase64ToUint8Array = (base64String: string) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  const checkExistingSubscription = async () => {
    if (!checkSupport()) return

    try {
      const registration = await navigator.serviceWorker.ready
      const sub = await registration.pushManager.getSubscription()
      
      if (sub) {
        subscription.value = sub
        isSubscribed.value = true
      }
    } catch (error) {
      console.error('Failed to check existing subscription:', error)
    }
  }

  return {
    isSupported,
    isSubscribed,
    subscription,
    checkSupport,
    requestPermission,
    subscribe,
    unsubscribe,
    sendTestNotification,
    checkExistingSubscription
  }
}
