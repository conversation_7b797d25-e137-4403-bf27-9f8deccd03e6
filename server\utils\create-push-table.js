import { getDbConnection } from './db.js'

export async function createPushSubscriptionsTable() {
  try {
    const db = await getDbConnection()
    
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS push_subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        endpoint VARCHAR(500) NOT NULL UNIQUE,
        p256dh VARCHAR(255) NOT NULL,
        auth VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `
    
    await db.execute(createTableQuery)
    console.log('Push subscriptions table created successfully')
    
  } catch (error) {
    console.error('Error creating push subscriptions table:', error)
    throw error
  }
}

// Run this if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createPushSubscriptionsTable()
    .then(() => {
      console.log('Database setup complete')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Database setup failed:', error)
      process.exit(1)
    })
}
