<template>
  <div class="fixed bottom-4 right-4 z-50">
    <!-- Simple PWA Panel -->
    <div
      v-if="showPanel"
      class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm mb-4"
    >
      <div class="flex items-center justify-between mb-3">
        <h3 class="font-semibold text-gray-900">PWA Status</h3>
        <button
          @click="showPanel = false"
          class="text-gray-400 hover:text-gray-600"
        >
          ✕
        </button>
      </div>

      <!-- Online Status -->
      <div class="mb-3">
        <div class="flex items-center gap-2">
          <div :class="isOnline ? 'bg-green-500' : 'bg-red-500'" class="w-2 h-2 rounded-full"></div>
          <span class="text-sm">{{ isOnline ? 'Online' : 'Offline' }}</span>
        </div>
      </div>

      <!-- Service Worker Status -->
      <div class="mb-3">
        <div class="text-sm font-medium mb-1">Service Worker</div>
        <div class="text-xs text-gray-600">{{ swStatus }}</div>
      </div>

      <!-- Test Button -->
      <button
        @click="testPWA"
        class="w-full text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200"
      >
        Test PWA Features
      </button>
    </div>

    <!-- Toggle Button -->
    <button
      v-if="!showPanel"
      @click="showPanel = true"
      class="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
      title="PWA Features"
    >
      ⚙️
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const showPanel = ref(false)
const isOnline = ref(navigator.onLine)
const swStatus = ref('Checking...')

onMounted(async () => {
  // Check service worker status
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.ready
      swStatus.value = registration.active ? 'Active' : 'Inactive'
    } catch (error) {
      swStatus.value = 'Error'
    }
  } else {
    swStatus.value = 'Not supported'
  }

  // Listen for online/offline status
  window.addEventListener('online', () => { isOnline.value = true })
  window.addEventListener('offline', () => { isOnline.value = false })
})

const testPWA = () => {
  alert('PWA Features:\n✅ Service Worker: ' + swStatus.value + '\n✅ Offline Support: Available\n✅ Caching: Active')
}
</script>
