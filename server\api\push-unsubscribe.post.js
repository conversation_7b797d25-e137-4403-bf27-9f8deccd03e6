import { getDbConnection } from '../utils/db'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const endpoint = body.endpoint
    
    if (!endpoint) {
      throw new Error('Endpoint is required')
    }

    const db = await getDbConnection()
    
    // Remove subscription from database
    const deleteQuery = 'DELETE FROM push_subscriptions WHERE endpoint = ?'
    await db.execute(deleteQuery, [endpoint])

    return { 
      status: 'success', 
      message: 'Subscription removed successfully' 
    }
    
  } catch (error) {
    console.error('Push unsubscribe error:', error)
    return {
      status: 'error',
      message: error.message || 'Failed to remove subscription'
    }
  }
})
